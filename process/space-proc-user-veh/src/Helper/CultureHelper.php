<?php

namespace App\Helper;

/**
 *  Culture Helper.
 */
class CultureHelper
{
    public static function parseCulture(string $culture): array
    {
        // Handle both underscore and dash separators
        $items = explode('_', $culture);
        if (count($items) === 1) {
            $items = explode('-', $culture);
        }

        return [
            'language' => $items[0] ?? '',
            'country' => isset($items[1]) ? strtoupper($items[1]) : ''
        ];
    }

    public static function createCulture(string $language, string $country, $separator = "_"): string
    {
        return  strtolower($language).$separator. strtoupper($country);
    }
}
