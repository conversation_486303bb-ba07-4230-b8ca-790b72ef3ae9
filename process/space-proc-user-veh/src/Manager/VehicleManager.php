<?php

namespace App\Manager;


use App\Dto\AddVehicleInputDTO;
use App\Dto\AddVehicleOutputDTO;
use App\Dto\EditVehicleInputDTO;
use App\Event\SpaceVehicleUpdatedEvent;
use App\Helper\BrandHelper;
use App\Helper\BrandProvider;
use App\Helper\CultureHelper;
use App\Helper\ErrorResponse;
use App\Helper\RefreshVehicleHelper;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\VehicleTypeEntities;

use App\Manager\Visual3DManager;
use App\Model\SystemVehicleData;
use App\Model\VehicleListV2\VehicleListResponseMapper;
use App\Model\VehicleModel;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Document\MileageData;
use App\Service\CorvetService;
use App\Service\FeatureCodeService;

use App\Service\RefreshVehicleInterface;
use App\Service\SystemSdprClient;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;


use App\Service\XFVehicleRefreshService;
use App\Service\XPVehicleRefreshService;
use App\Manager\SystemUserDBManager;
use App\Connector\SystemOmniConnector;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use App\Helper\WSResponse;
use Throwable;
use Webmozart\Assert\Assert;
use App\Manager\CatalogManager;
use App\Manager\SubscriptionManager;
use App\Mapper\EditVehicleMapper;
use App\Service\SystemUserDBService;
use App\Service\VehicleDataService;
use App\Service\VehicleMapperService;
use App\Service\VehicleTransformationService;
use App\Service\VehicleValidationService;
use App\Service\VehicleLabelService;
use App\Service\VehicleOrderService;
use App\Service\VehicleFeatureService;
use App\Service\VehicleCorvetService;
use App\Service\VehicleUtilityService;
use stdClass;
use App\Model\FeatureCode;

/**
 * Legacy UserDataDocument class for backward compatibility with refresh services
 */
class LegacyUserDataDocument
{
    public string $userId;
}

/**
 * User vehicle Manager.
 */
class VehicleManager
{
    use LoggerTrait;

    private const ELIGIBILITY_NAVCOZAR = 'navcozar';
    private const ELIGIBILITY_REMOTELEV = 'remotelev';
    private const ELIGIBILITY_NAC = 'nac';

    public function __construct(
        private SerializerInterface $serializer,
        private DenormalizerInterface $denormalizer,
        private ValidatorInterface $validator,
        private EventDispatcherInterface $dispatcher,
        private XPVehicleRefreshService $xPVehicleRefreshService,
        private XFVehicleRefreshService $xFVehicleRefreshService,
        private UserDataService $userDataService,
        private CorvetService $corvetService,
        private SystemUserDataClient $systemUserDataClient,
        private Visual3DManager $visual3DManager,
        private BrandHelper $brandHelper,
        private SystemSdprClient $systemSdprClient,
        private FeatureCodeService $featureCodeService,
        private VehicleLabelService $vehicleLabelService,
        private NormalizerInterface $normalizer,
        private CatalogManager $catalogManager,
        private SubscriptionManager $subscriptionManager,
        private SystemUserDBService $systemUserDBService,
        private SystemUserDBManager $systemUserDBManager,
        private SystemOmniConnector $sysOmniConnector,
        // New service layer dependencies following microservice architecture patterns
        private VehicleDataService $vehicleDataService,
        private VehicleMapperService $vehicleMapperService,
        private VehicleTransformationService $vehicleTransformationService,
        private VehicleValidationService $vehicleValidationService,
        // Additional specialized services for refactoring
        private VehicleOrderService $vehicleOrderService,
        private VehicleFeatureService $vehicleFeatureService,
        private VehicleCorvetService $vehicleCorvetService,
        private VehicleUtilityService $vehicleUtilityService
    ) {
    }

    /**
     * Get Vehicles On Order (migrated to ODM).
     */
    public function getVehicles(string $userId): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicles on order for user', [
                'userId' => $userId,
            ]);

            // Use ODM to get user data
            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found in MongoDB ODM', [
                    'userId' => $userId,
                    'message' => 'No user document found - this could be normal if user has no data yet'
                ]);
                return new SuccessResponse(['vehicles' => []]);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User found in MongoDB ODM', [
                'userId' => $userId,
                'userDataId' => $userData->getId(),
            ]);

            $vehicles = $userData->getVehicles();
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Retrieved vehicles from user document', [
                'userId' => $userId,
                'totalVehicles' => count($vehicles),
            ]);

            $vehicleModels = [];
            $vehicleIds = [];
            $onOrderCount = 0;
            $notOnOrderCount = 0;

            foreach ($vehicles as $index => $vehicle) {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Processing vehicle', [
                    'userId' => $userId,
                    'vehicleIndex' => $index,
                    'vin' => $vehicle->getVin(),
                    'brand' => $vehicle->getBrand(),
                    'model' => $vehicle->getModel(),
                    'status' => $vehicle->getStatus(),
                    'versionId' => $vehicle->getVersionId(),
                ]);

                // Filter for vehicles on order
                $isOrder = $this->vehicleOrderService->isVehicleOnOrder($vehicle);

                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order status check', [
                    'userId' => $userId,
                    'vin' => $vehicle->getVin(),
                    'isOrder' => $isOrder,
                    'status' => $vehicle->getStatus(),
                ]);

                if ($isOrder) {
                    $onOrderCount++;
                    $vehicleId = $vehicle->getDocumentId() ?? $vehicle->getVin(); // Using documentId as ID, fallback to VIN
                    $vehicleIds[] = $vehicleId;

                    // Convert ODM Vehicle to the expected API response format
                    $vehicleData = $this->vehicleTransformationService->convertODMVehicleToApiFormat($vehicle, $userId);
                    $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting vehicle to API format', [
                        'userId' => $userId,
                        'vin' => $vehicle->getVin(),
                        'vehicleData' => $vehicleData,
                    ]);

                    $vehicleModels[] = $vehicleData;
                } else {
                    $notOnOrderCount++;
                }
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle filtering completed', [
                'userId' => $userId,
                'totalVehicles' => count($vehicles),
                'onOrderVehicles' => $onOrderCount,
                'notOnOrderVehicles' => $notOnOrderCount,
                'finalVehicleModels' => count($vehicleModels),
            ]);

            // Mark orders as read (if this functionality is still needed)
            if (!empty($vehicleIds)) {
                $this->markOrdersAsRead($userId, $vehicleIds);
            }

            // Return the vehicles data directly in the expected format
            // The SuccessResponse will wrap this in a 'success' object, giving us {"success": {"vehicles": [...]}}
            return new SuccessResponse(['vehicles' => $vehicleModels]);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception VehicleManager::getVehicles ' . $e->getMessage(), [
                'userId' => $userId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }









    /**
     * Set isUpdated to false so the front knows the new orders from already seen ones.
     * Delegated to VehicleOrderService.
     */
    public function markOrdersAsRead(string $userId, array $vehicleIds): void
    {
        $this->vehicleOrderService->markOrdersAsRead($userId, $vehicleIds);
    }

    /**
     * create or update vehicle data function.
     */
    public function createOrUpdateVehicle(array $content): ResponseArrayFormat
    {
        try {
            $userId = $content['userId'] ?? null;
            $vehicleModel = $this->getVehicleModel($content);
            $errorsMessages = $this->vehicleValuesValidate($vehicleModel);
            if (empty($userId)) {
                $errorsMessages[] = 'The user ID value should not be blank.';
            }
            if ($errorsMessages) {
                return new ErrorResponse($errorsMessages);
            }

            // Use ODM-based vehicle creation/update instead of legacy VehicleService
            $success = $this->vehicleDataService->createOrUpdateVehicleODM($userId, $vehicleModel);
            if ($success) {
                $data = [
                    'message' => 'Vehicle data has been saved successfully',
                    'mopId' => $content['mopId'],
                ];

                $spaceVehicleUpdatedEvent = new SpaceVehicleUpdatedEvent(
                    $userId,
                    $vehicleModel,
                    $content
                );
                $this->dispatcher->dispatch(
                    $spaceVehicleUpdatedEvent,
                    SpaceVehicleUpdatedEvent::class
                );
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof ErrorResponse) {
                    $this->logger->error(
                        'An error has occurred while sync MyMarque vehicle data with Space',
                        [
                            'userId' => $userId,
                            'model' => $vehicleModel,
                            'error' => $spaceVehicleUpdatedEvent->getResponse()->toArray(),
                        ]
                    );
                }
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof SuccessResponse) {
                    // adding logic to update the flag isUpdated to true for the vehicle
                }

                return new SuccessResponse($data);
            }
            $data = [
                'raison' => 'An error has occurred, vehicle data has not been saved',
                'mopId' => $content['mopId'],
            ];

            return new ErrorResponse($data);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * check input errors.
     */
    public function vehicleValuesValidate(VehicleModel $vehicleModel): array
    {
        $errors = $this->validator->validate($vehicleModel);
        $messages = [];
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $this->logger->error(__METHOD__ . ' : VehicleModel validation error', [
                    'property' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                    'value' => $error->getInvalidValue()
                ]);
                $messages[] = $error->getMessage();
            }
        }
        $errors = $this->validator->validate($vehicleModel->getVehicleOrder());
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $this->logger->error(__METHOD__ . ' : VehicleOrder validation error', [
                    'property' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                    'value' => $error->getInvalidValue()
                ]);
                $messages[] = $error->getMessage();
            }
        }

        return $messages;
    }

    /**
     * mapping content like a array data.
     */
    public function getVehicleModel(array $content): VehicleModel
    {
        $culture = CultureHelper::parseCulture($content['culture']);
        $content['country'] = $culture['country'];
        $content['language'] = $culture['language'];

        // Get vehicle data from Corvet service first to populate versionId and other fields
        if (isset($content['vin']) && isset($content['brand'])) {
            try {
                $corvetData = $this->vehicleCorvetService->getCorvetData($content['vin'], $content['brand']);
                $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
                if ($lcdv) {
                    $content['versionId'] = $lcdv;

                    // Get vehicle label from vehicle label service
                    try {
                        $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
                        $vehicleSettings = json_decode($response->getData(), true)['documents'][0] ?? [];
                        if (!empty($vehicleSettings['label'])) {
                            $content['label'] = $vehicleSettings['label'];
                        }
                    } catch (\Exception $e) {
                        $this->logger->warning(__METHOD__ . ' : Error getting vehicle label', [
                            'lcdv' => $lcdv,
                            'error' => $e->getMessage()
                        ]);
                    }

                    // Extract additional vehicle data from Corvet response
                    $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
                    if (!empty($vehicleData)) {
                        // Map additional fields from Corvet data
                        $content['type'] = $vehicleData['TYPE'] ?? '';
                        $content['year'] = $vehicleData['ANNEE_MODELE'] ?? '';
                        // Add other fields as needed
                    }
                }
            } catch (\Exception $e) {
                $this->logger->error(__METHOD__ . ' : Error getting vehicle data from Corvet', [
                    'vin' => $content['vin'],
                    'brand' => $content['brand'],
                    'error' => $e->getMessage()
                ]);
                // Continue with the original flow even if Corvet fails
            }
        }

        $content = $this->manageOVData($content);

        // Ensure required fields have default values
        if (empty($content['id'])) {
            $content['id'] = uniqid('vehicle_', true);
        }
        if (empty($content['label'])) {
            $content['label'] = 'Vehicle ' . ($content['brand'] ?? 'Unknown');
        }
        if (empty($content['versionId'])) {
            $content['versionId'] = 'DEFAULT_VERSION_ID';
        }
        if (empty($content['visual'])) {
            $content['visual'] = BrandProvider::getBrandDefaultImage($content['brand']);
        }
        if (empty($content['isOrder'])) {
            $content['isOrder'] = false;
        }

        $content['vehicleOrder'] = [
            'mopId' => $content['mopId'] ?? '',
            'orderFormId' => strval($content['orderFormId'] ?? 'DEFAULT_ORDER_FORM_ID'),
            'trackingStatus' => $content['trackingStatus'] ?? 'DELIVERED_CUSTOMER',
            'orderFormStatus' => $content['orderFormStatus'] ?? 'TO_BE_VALIDATED',
        ];

        return $this->serializer->deserialize(json_encode($content), VehicleModel::class, 'json');
    }

    /**
     * get vehicle data with GB cases.
     */
    public function manageOVData(array $vehicle): array
    {
        if ($this->vehicleCorvetService->isOVVehicle($vehicle['versionId'])) {
            $allAttributes = $this->vehicleCorvetService->getVinAttributes($vehicle['vin'], 'OP'); /* forcing OP brand */
            $brand = $vehicle['brand'] = $this->vehicleCorvetService->getVehicleBrand($allAttributes, $vehicle['country']);
            if ('VX' == $brand) {
                $vehicle['language'] = 'en';
                $vehicle['country'] = 'GB';
            }
        }

        return $vehicle;
    }

    /**
     * Get Vehicles Summary.
     */
    public function getVehicleSummary(string $vehicleId, string $userId): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle summary', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
            ]);

            $vehicle = $this->vehicleUtilityService->getVehicleInfo($vehicleId, $userId);
            $vehicleOrder = $vehicle->getVehicleOrder();
            $brand = $vehicle->getBrand();
            $country = $vehicle->getCountry();
            if (empty($country)) {
                $country = 'AT';
            }
            $orderFormId = $vehicleOrder->getOrderFormId();
            $mopId = $vehicleOrder->getMopId();

            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order information', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'brand' => $brand,
                'country' => $country,
                'orderFormId' => $orderFormId,
                'mopId' => $mopId,
            ]);

            // Check if we have valid order information
            if (empty($orderFormId) || empty($mopId)) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' No order information available for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                    'orderFormId' => $orderFormId,
                    'mopId' => $mopId,
                ]);
                return new ErrorResponse('No order information available for this vehicle', Response::HTTP_NOT_FOUND);
            }

            $response = $this->vehicleOrderService->getOrderSummary($orderFormId, $mopId, $brand, $country);
            if (Response::HTTP_OK == $response->getCode()) {
                return new SuccessResponse(['url' => $response->getData()['success']['pdf_url'] ?? '']);
            }
            $errorMessage = $response->getData()['error']['message'] ?? 'server error';
            $this->logger->error(__METHOD__ . ' Error response ' . $errorMessage);

            return new ErrorResponse($errorMessage, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }



    public function getUserVehiclesData(
        string $userId,
        string $brand,
        string $language,
        string $country
    ): ResponseArrayFormat {
        try {
            // retrieve user data from MongoDB ODM, if exists
            $odmUserData = $this->userDataService->findUserById($userId);
            $this->logger->debug(__METHOD__ . ' Initial user vehicles data retrieved', [
                'userId' => $userId,
                'userDataDocument' => $odmUserData ? get_class($odmUserData) : 'null'
            ]);

            // Use ODM UserData directly with refresh services (no conversion needed)
            // retrieve vehicles data for xF brands
            $xfVehicles = $this->xFVehicleRefreshService->retrieveVehicles($odmUserData, $brand, $language, $country);

            // retrieve vehicles data for xP brands
            $xpVehicles = $this->xPVehicleRefreshService->retrieveVehicles($odmUserData, $brand, $language, $country);

            $this->logger->debug(__METHOD__ . ' User vehicles data refreshed data retried', [
                'xfVehicles' => $xfVehicles,
                'xpVehicles' => $xpVehicles,
            ]);

            // Update all vehicles
            $stellantisVehicles = array_merge($xpVehicles, $xfVehicles);
            foreach ($stellantisVehicles as $vehicle) {
                Assert::isInstanceOf($vehicle, SystemVehicleData::class);

                // update vehicles data on MongoDB for the user
                $this->vehicleDataService->saveUserDataDocumentVehicles($userId, $vehicle->getVin(), $vehicle, $country);
            }

            // for GSDP sdp remove all vehicles that are not in the list        
            $this->userDataService->removeUserGSPDVehicles($userId, $brand, array_keys($xfVehicles));

            // read a fresh copy of vehicles data from MongoDB
            $userVehicles = $this->userDataService->getVehicleByUserIdAndBrand($userId, $brand);
            $this->logger->debug(__METHOD__ . ' User vehicles data refreshed successfully', [
                'userId' => $userId,
                'vehicleCount' => count($userVehicles)
            ]);

            $vehiclesList = [];
            foreach ($userVehicles as $userVehicle) {
                Assert::isInstanceOf($userVehicle, Vehicle::class);

                // Convert ODM Vehicle to legacy format for the mapper
                $legacyVehicleData = $this->vehicleUtilityService->convertODMVehicleToArray($userVehicle);

                // Create a simple object with the required properties for the mapper
                $legacyVehicle = (object) $legacyVehicleData;
                $legacyVehicle->vin = $legacyVehicleData['vin'];
                $legacyVehicle->id = $legacyVehicleData['id'];
                $legacyVehicle->versionId = $legacyVehicleData['versionId'];
                $legacyVehicle->nickName = $userVehicle->getNickName();
                $legacyVehicle->shortLabel = $legacyVehicleData['label'];
                $legacyVehicle->picture = $userVehicle->getPicture();
                $legacyVehicle->sdp = $userVehicle->getStatus() ?? '';
                $legacyVehicle->isOrder = $legacyVehicleData['isOrder'];
                $legacyVehicle->warrantyStartDate = $userVehicle->getWarrantyStartDate();
                $legacyVehicle->vehicleOrder = null; // Will be set if order data exists

                $vehicleListResponse = VehicleListResponseMapper::mapUserDataDocumentVehicle($legacyVehicle);
                $vehiclesList[] = $this->normalizer->normalize($vehicleListResponse, null, ['groups' => ['xp_vehicle_list']]);
            }

            return new SuccessResponse($vehiclesList, Response::HTTP_OK);

        } catch (Throwable $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception', [
                'userId' => $userId,
                'brand' => $brand,
                'country' => $country,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new ErrorResponse(
                'An error occurred while fetching user vehicles. ' . $e->getMessage(),
                Response::HTTP_BAD_REQUEST
            );
        }
    }



    public function addSSDPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            if (!$this->vehicleValidationService->validateSsdpBrand($vehicleDto->getBrand())) {
                $this->logger->error(__METHOD__ . ' : Error brand not SSDP');
                return new ErrorResponse($vehicleDto->getBrand() . ' Brand not supported', Response::HTTP_BAD_REQUEST);
            }
            $vehicle = $this->vehicleUtilityService->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if ($vehicle) {
                $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
                return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
            }

            $userData = $this->userDataService->findUserById($vehicleDto->getUserId());
            $userDbId = $this->vehicleUtilityService->extractUserDbIdFromODMUserData($userData);
            if (!$userDbId) {
                $this->logger->error(__METHOD__ . ' : Error userDbId not found');
                return new ErrorResponse('User DB ID not found', Response::HTTP_NOT_FOUND);
            }
            $corvetData = $this->vehicleCorvetService->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
            $this->logger->debug(__METHOD__ . ' Corvet data', [
                'corvetData' => $corvetData
            ]);
            $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
            $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
            $vehicleSettings = json_decode($response->getData(), true)['documents'][0] ?? [];
            $this->logger->debug(__METHOD__ . ' Vehicle settings', [
                'vehicleSettings' => $vehicleSettings
            ]);
            $label = $vehicleSettings['label'] ?? null;
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $visualUrl = $vehicleSettings['defaultImage'] ?? null;
            $sdp = $vehicleSettings['sdp'] ?? '';
            if (!$label) {
                $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
                return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
            }
            $params = [
                'vin' => $vehicleDto->getVin(),
                'brand' => $vehicleDto->getBrand(),
                'country' => $vehicleDto->getCountry(),
                'userId' => $vehicleDto->getUserId(),
                'commercialName' => $label,
                'pictureUrl' => $visualUrl
            ];
            $params = array_filter($params, function ($value) {
                return $value !== null;
            });

            $addVehicleInUserDbResponse = $this->systemUserDBManager->addVehicle($userDbId, $params);
            $this->logger->info("".__METHOD__." => Response from add vehicle in user db --> " . ['code' => $addVehicleInUserDbResponse->getCode(), 'data' => $addVehicleInUserDbResponse->getData()]);
            if ($addVehicleInUserDbResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error adding vehicle in user db');
                $data = $addVehicleInUserDbResponse->getData();
                $message = $data['error']['message'] ?? $data;
                $errorResponse = new ErrorResponse($message, $addVehicleInUserDbResponse->getCode());
                $errorResponse->setErrors($data['error']['errors'] ?? []);
                return $errorResponse;
            }
            $vehicleOutputDto = new AddVehicleOutputDTO();
            $corvertAttributs = $this->vehicleFeatureService->getManagedAttributesByPrefix($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
            $vehicleOutputDto->setAddStatus('COMPLETE');
            $vehicleOutputDto->setSdp($sdp);
            $vehicleOutputDto->setWarrantyStartDate($vehicleData['VEH_WARRANTY_START_DATE'] ?? null);
            $vehicleOutputDto->setRegTimestamp(time());
            $vehicleOutputDto->setType($vehicleType);
            $vehicleOutputDto->setLabel($label);
            $vehicleOutputDto->setLcdv($lcdv);
            $vehicleOutputDto->setVisual($visualUrl);
            $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode($vehicleDto->getUserId(), $vehicleDto->getVin(), $lcdv, $vehicleType, $corvertAttributs, null, null, true, $userDbId));
            $vehicleOutputDto->setFeatureCodeExpiryTo24HoursFromNow();
            $vehicleOutputDto->setIsOrder(false);
            $vehicleOutputDto->setMake($vehicleDto->getBrand());
            $vehicleOutputDto->setMarket($vehicleDto->getCountry());
            $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(), $vehicleDto->getCountry()));
            $vehicleOutputDto->setIsO2X($isO2x);
            $vehicleOutputDto->setLastUpdate(time());
            // calling mongoDb to push new vehicle
            $mongodbUpdateResponse = $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
            if ($mongodbUpdateResponse->getCode() == Response::HTTP_OK) {
                $this->logger->info(__METHOD__ . ' : vehicle added successfully in the mongoDb');
                return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
            }
            $this->logger->error(__METHOD__ . " : Failed to add vehicle. Code: " . $mongodbUpdateResponse->getCode() . ". Message: " . $mongodbUpdateResponse->getData()['error']['message'] ?? $mongodbUpdateResponse->getData());
            return new ErrorResponse($mongodbUpdateResponse->getData(), $mongodbUpdateResponse->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . " : Error adding vehicle in user db", ['vin' => $vehicleDto->getVin(), 'exception' => $e->getMessage()]);
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }


    public function mockCorvetData(): array
    {
        $array = [
            "ENTETE" => [
                "EMETTEUR" => "MYM_PREPROD"
            ],
            "VEHICULE" => [
                "@attributes" => [
                    "Existe" => "O"
                ],
                "DONNEES_VEHICULE" => [
                    "WMI" => "VYE",
                    "VDS" => "ATTEN0",
                    "VIS" => "SPU00024",
                    "VIN" => "ZARHBTTG3R9011445",
                    "LCDV_BASE" => "1JJPSYNZDFL0A0A0M0ZZGHFY",
                    "N_APV_PR" => [],
                    "ANNEE_MODELE" => "0A",
                    "MARQUE_COMMERCIALE" => "0J",
                    "DATE_DEBUT_GARANTIE" => [],
                    "DATE_ENTREE_COMMERCIALISATION" => "18/12/2024 17:23:00",
                    "LIGNE_DE_PRODUIT" => "JP"
                ],
                "LISTE_ATTRIBUTES_7" => [
                    "ATTRIBUT" => [
                        "D0000CD",
                        "D0103CD",
                        "D0202CD",
                        "D0301CD",
                        "D0400CD",
                        "D0500CD",
                        "D0600CD",
                        "D0701CD",
                        "D0800CD",
                        "D0900CD",
                        "D1000CD",
                        "D1100CD",
                        "D1202CD",
                        "D1301CD",
                        "D1500CD",
                        "D1701CD",
                        "D1801CD",
                        "D1901CD",
                        "D1C03CD",
                        "D1D05CD",
                        "D1E55CD",
                        "D1F24CD",
                        "D1G30CD",
                        "D1H09CD",
                        "D2501CD",
                        "D2802CD",
                        "D2900CD",
                        "D2A00CD",
                        "D3201CD",
                        "D4100CD",
                        "D4401CD",
                        "D4I00CD",
                        "D4K00CD",
                        "D5000CD",
                        "D5102CD",
                        "D5901CD",
                        "D5M04CD",
                        "D5N08CD",
                        "D5O01CD",
                        "D6007CD",
                        "D6100CD",
                        "D6200CD",
                        "D6302CD",
                        "D6404CD",
                        "D6508CD",
                        "D6602CD",
                        "D6706CD",
                        "D6803CD",
                        "D6D03CD",
                        "D6E01CD",
                        "D6F02CD",
                        "D6G03CD",
                        "D6H05CD",
                        "D6J00CD",
                        "D6K04CD",
                        "D6L04CD",
                        "D6N01CD",
                        "D6O00CD",
                        "D6Q01CD",
                        "D6V07CD",
                        "D6W02CD",
                        "D6X03CD",
                        "D7003CD",
                        "D7A01CD",
                        "D7B02CD",
                        "D7C02CD",
                        "D7E00CD",
                        "D7H01CD",
                        "D7K02CD",
                        "D7L00CD",
                        "D7P02CD",
                        "D7Q02CD",
                        "D7S02CD",
                        "D7T02CD",
                        "D7U01CD",
                        "D7V02CD",
                        "D7W02CD",
                        "D7X00CD",
                        "D7Z04CD",
                        "DA300CD",
                        "DA401CD",
                        "DA516CD",
                        "DA639CD",
                        "DA702CD",
                        "DAB00CD",
                        "DAE00CD",
                        "DAF01CD",
                        "DAGCDCD",
                        "DAH02CD",
                        "DAJ01CD",
                        "DAK06CD",
                        "DAL45CD",
                        "DAO05CD",
                        "DAP01CD",
                        "DAQ00CD",
                        "DAR29CD",
                        "DAS03CD",
                        "DAU02CD",
                        "DAZ10CD",
                        "DBF01CD",
                        "DBJ03CD",
                        "DBK60CD",
                        "DBS00CD",
                        "DBU11CD",
                        "DCD00CD",
                        "DCF14CD",
                        "DCG18CD",
                        "DCK04CD",
                        "DCL12CD",
                        "DCN04CD",
                        "DCO01CD",
                        "DCP01CD",
                        "DCQ06CD",
                        "DCU22CD",
                        "DCX01CD",
                        "DD429CD",
                        "DD606CD",
                        "DDA41CD",
                        "DDC00CD",
                        "DDD04CD",
                        "DDE02CD",
                        "DDG00CD",
                        "DDH82CD",
                        "DDI00CD",
                        "DDJ03CD",
                        "DDO01CD",
                        "DDR07CD",
                        "DDT00CD",
                        "DDX02CD",
                        "DDY23CD",
                        "DDZI7CD",
                        "DE201CD",
                        "DE301CD",
                        "DE704CD",
                        "DE803CD",
                        "DED44CD",
                        "DEE37CD",
                        "DEF00CD",
                        "DEG23CD",
                        "DEHEOCD",
                        "DEJ06CD",
                        "DEK08CD",
                        "DEL01CD",
                        "DENWWCD",
                        "DES03CD",
                        "DEZZZCD",
                        "DFG12CD",
                        "DFH05CD",
                        "DFI08CD",
                        "DFT02CD",
                        "DFU00CD",
                        "DFX00CD",
                        "DGA01CD",
                        "DGH01CD",
                        "DGMAZCD",
                        "DGQ22CD",
                        "DGY08CD",
                        "DGZ00CD",
                        "DHB39CD",
                        "DHE00CD",
                        "DHG06CD",
                        "DHJ00CD",
                        "DHM00CD",
                        "DHU24CD",
                        "DHY03CD",
                        "DI202CD",
                        "DI301CD",
                        "DI402CD",
                        "DI501CD",
                        "DI604CD",
                        "DI702CD",
                        "DI801CD",
                        "DI901CD",
                        "DIB01CD",
                        "DIF10CD",
                        "DIM18CD",
                        "DIN07CD",
                        "DIO02CD",
                        "DIP03CD",
                        "DIQ02CD",
                        "DIT14CD",
                        "DIU16CD",
                        "DIW00CD",
                        "DJA25CD",
                        "DJB04CD",
                        "DJD02CD",
                        "DJQ00CD",
                        "DJY11CD",
                        "DK303CD",
                        "DK906CD",
                        "DKU03CD",
                        "DKX41CD",
                        "DL311CD",
                        "DL600CD",
                        "DL700CD",
                        "DL801CD",
                        "DL900CD",
                        "DLA10CD",
                        "DLB13CD",
                        "DLD00CD",
                        "DLE12CD",
                        "DLI16CD",
                        "DLN03CD",
                        "DLV02CD",
                        "DLW02CD",
                        "DLX54CD",
                        "DLZ06CD",
                        "DMG08CD",
                        "DMH00CD",
                        "DMI61CD",
                        "DMJAKCD",
                        "DMO13CD",
                        "DMW13CD",
                        "DN100CD",
                        "DN400CD",
                        "DN510CD",
                        "DN706CD",
                        "DN803CD",
                        "DN904CD",
                        "DNA09CD",
                        "DNB08CD",
                        "DNC05CD",
                        "DNF15CD",
                        "DNG00CD",
                        "DNH01CD",
                        "DNK05CD",
                        "DNM00CD",
                        "DNN01CD",
                        "DNR00CD",
                        "DO103CD",
                        "DO301CD",
                        "DO409CD",
                        "DO506CD",
                        "DO813CD",
                        "DO906CD",
                        "DOA04CD",
                        "DOCADCD",
                        "DOD02CD",
                        "DOK00CD",
                        "DOL11CD",
                        "DOP01CD",
                        "DOR03CD",
                        "DOS01CD",
                        "DOY25CD",
                        "DPDZCCD",
                        "DPKADCD",
                        "DPLNVCD",
                        "DPQ02CD",
                        "DPR04CD",
                        "DPS02CD",
                        "DPY13CD",
                        "DQA05CD",
                        "DQB48CD",
                        "DQC00CD",
                        "DQF00CD",
                        "DQH20CD",
                        "DQJ04CD",
                        "DQK15CD",
                        "DQS10CD",
                        "DQT00CD",
                        "DQV03CD",
                        "DQX01CD",
                        "DRA01CD",
                        "DRC71CD",
                        "DRE24CD",
                        "DRG40CD",
                        "DRH14CD",
                        "DRI00CD",
                        "DRJ05CD",
                        "DRK05CD",
                        "DRP02CD",
                        "DRQ01CD",
                        "DRS19CD",
                        "DRT21CD",
                        "DRU20CD",
                        "DRZ89CD",
                        "DSB00CD",
                        "DSD04CD",
                        "DSH02CD",
                        "DSO01CD",
                        "DSP16CD",
                        "DTC00CD",
                        "DTG09CD",
                        "DTJ02CD",
                        "DUB24CD",
                        "DUC00CD",
                        "DUE05CD",
                        "DUF01CD",
                        "DUR00CD",
                        "DUV37CD",
                        "DUW00CD",
                        "DUZ19CD",
                        "DVD09CD",
                        "DVF37CD",
                        "DVH37CD",
                        "DVKAICD",
                        "DVO01CD",
                        "DVQ72CD",
                        "DVS05CD",
                        "DVU01CD",
                        "DVW00CD",
                        "DVX23CD",
                        "DWAICCD",
                        "DXA00CD",
                        "DXC11CD",
                        "DXD04CD",
                        "DXF00CD",
                        "DXG24CD",
                        "DXQAZCD",
                        "DXU00CD",
                        "DXZ01CD",
                        "DYB01CD",
                        "DYC00CD",
                        "DYE01CD",
                        "DYH02CD",
                        "DYI45CD",
                        "DYK13CD",
                        "DYM25CD",
                        "DYP00CD",
                        "DYQ02CD",
                        "DYR22CD",
                        "DYT31CD",
                        "DYU03CD",
                        "DYV19CD",
                        "DYW25CD",
                        "DZE34CD",
                        "DZICUCD",
                        "DZJFVCD",
                        "DZVNICD",
                        "DZZ0JCD",
                        "T1AADG",
                        "T1BADG",
                        "T9AADG",
                        "T9BADG",
                        "T9CADG",
                        "T9DADG",
                        "NEW1CD",
                        "NEW2CD"
                    ]
                ]
            ]
        ];
        return $array;
    }

    public function addXPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        if (!$this->vehicleValidationService->validateSsdpBrand($vehicleDto->getBrand())) {
            $this->logger->error(__METHOD__ . ' : Error brand not SSDP');
            return new ErrorResponse('Brand not supported', Response::HTTP_BAD_REQUEST);
        }
        $vehicle = $this->vehicleUtilityService->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
        if ($vehicle) {
            $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
            return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
        }
        // call corvet
        $corvetData = $this->vehicleCorvetService->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
        //retrive lcdv
        $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
        if (!$lcdv) {
            $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
            return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
        }
        // 
        $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
        $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
        $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
        $vehicleSettings = json_decode($response->getData(), true)['documents'][0] ?? [];
        //call mongoDb vehicleLabel to get label
        $label = $vehicleSettings['label'] ?? null;
        $isO2x = $vehicleSettings['isO2x'] ?? false;
        if (!$label) {
            $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
            return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
        }
        $visualResponse = $this->visual3DManager->loadImages(
            $lcdv,
            $vehicleDto->getBrand(),
            $vehicleDto->getSource(),
            $this->getDataFromAttributes($allAttributes)
        );

        $visualUrl = $visualResponse['data'] ?? null;

        //call mongodb to get psaId = ACNT
        $psaId = $this->getPsaId($vehicleDto->getUserId(), $vehicleDto->getBrand());
        if (!$psaId) {
            $this->logger->error(__METHOD__ . ' : Error psaId not found while calling mongoDb userData');
            return new ErrorResponse('PsaId not found ', Response::HTTP_NOT_FOUND);
        }

        $siteCode = RefreshVehicleHelper::getSiteCode($vehicleDto->getBrand(), $vehicleDto->getCountry(), $vehicleDto->getSource());
        //call C@ to get ticket
        $ticket = $this->getTicket($psaId, $siteCode);
        if (!$ticket) {
            $this->logger->error(__METHOD__ . ' : Error Ticket not found while calling C@');
            return new ErrorResponse('Ticket not found ', Response::HTTP_NOT_FOUND);
        }

        // add vehicle to customer@
        $response = $this->addVehicleInCustomerAt(
            $vehicleDto->getVin(),
            $label,
            $lcdv,
            $ticket,
            $siteCode,
            $vehicleDto->getLanguage()
        );

        if (!$response) {
            $this->logger->error(__METHOD__ . ' : Error while adding vehicle in C@ VIN : ' . $vehicleDto->getVin());
            return new ErrorResponse('Error while adding vehicle in C@ ', Response::HTTP_BAD_REQUEST);
        }
        $vehicleOutputDto = new AddVehicleOutputDTO();
        $corvertAttributs = $this->vehicleFeatureService->getManagedAttributesByPrefix($allAttributes);
        $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
        $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
        $vehicleOutputDto->setAddStatus('COMPLETE');
        $sdp = $this->vehicleUtilityService->getSdp($vehicleSettings['sdp']);
        $vehicleOutputDto->setSdp($sdp);
        $vehicleOutputDto->setWarrantyStartDate($vehicleData['DATE_DEBUT_GARANTIE'] ?? '');
        $vehicleOutputDto->setRegTimestamp(time());
        $vehicleOutputDto->setType($vehicleType);
        $vehicleOutputDto->setLabel($label);
        $vehicleOutputDto->setLcdv($lcdv);
        $vehicleOutputDto->setVisual($visualUrl);
        // $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode($lcdv, $corvertAttributs));
        $vehicleOutputDto->setIsOrder(false);
        $vehicleOutputDto->setMake($vehicleDto->getBrand());
        $vehicleOutputDto->setMarket($vehicleDto->getCountry());
        $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(), $vehicleDto->getCountry()));
        $vehicleOutputDto->setIsO2X($isO2x);
        $vehicleOutputDto->setLastUpdate(time());
        // calling mongoDb to push new vehicle
        $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
        return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
    }

    public function editVehicle(EditVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            $vehicleData = $this->vehicleUtilityService->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if (!$vehicleData) {
                return new ErrorResponse('Vehicle not exists', Response::HTTP_NOT_FOUND);
            }

            // Extract ODM Vehicle object from the response
            $vehicleObject = $vehicleData['vehicle'][0] ?? null;
            if (!$vehicleObject) {
                return new ErrorResponse('Vehicle object not found', Response::HTTP_NOT_FOUND);
            }

            // Get vehicle properties from ODM Vehicle object
            $sdp = $vehicleObject->getStatus(); // ODM uses status field instead of sdp
            $shortLabel = $vehicleObject->getLabel() ?? ''; // ODM uses label field instead of shortLabel
            $picture = $vehicleObject->getPicture() ?? ''; // ODM Vehicle now has picture field

            // Get current mileage from ODM Vehicle object
            $currentMileage = 0;
            $mileageData = $vehicleObject->getMileage();
            if ($mileageData) {
                $currentMileage = $mileageData->getValue() ?? 0;
            }

            // Get current nickName and licencePlate from ODM Vehicle object
            $currentNickName = $vehicleObject->getNickName() ?? ''; // ODM Vehicle now has nickName field
            $currentLicencePlate = $vehicleObject->getRegistrationNumber() ?? '';

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle data extracted from ODM', [
                'vin' => $vehicleObject->getVin(),
                'sdp' => $sdp,
                'shortLabel' => $shortLabel,
                'currentMileage' => $currentMileage,
                'currentNickName' => $currentNickName,
                'currentLicencePlate' => $currentLicencePlate,
            ]);

            $vehicleDto->setCommercialName($shortLabel);
            $vehicleDto->setPictureUrl($picture);

            if ($sdp != RefreshVehicleInterface::SDP_SSDP) {
                return new ErrorResponse('Vehicle not managed in user repository', Response::HTTP_NOT_FOUND);
            }

            $customerId = $vehicleData['userDbId'] ?? null;
            if (!$customerId) {
                return new ErrorResponse('User not existe in user repository', Response::HTTP_NOT_FOUND);
            }

            $vehicleDto->setCustomerId($customerId);
            $response = $this->systemUserDBService->updateCustomerGarage($vehicleDto);

            if ($response->getCode() == Response::HTTP_OK) {
                $vehicleDto->setMileageDate(time());
                $vehicleDto->setMileage($vehicleDto->getMileage() !== null ? (int) $vehicleDto->getMileage() : $currentMileage);
                $vehicleDto->setNickName($vehicleDto->getNickName() ?: $currentNickName);
                $vehicleDto->setLicencePlate($vehicleDto->getLicencePlate() ?: $currentLicencePlate);
                $this->updateVehicle($vehicleDto);
                return new SuccessResponse(EditVehicleMapper::map($vehicleDto), Response::HTTP_OK);
            }

            return (new ErrorResponse($response->getData()['error']['message'] ?? '', $response->getCode()))->setErrors($response->getData()['error']['errors'] ?? []);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception VehicleManager::editVehicle ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }



    public function setEligibilityFromContracts(?array $subscriptions): array
    {
        $eligibilities = [];
        foreach ($subscriptions as $subscription) {

            $eligibility = $subscription['type'] ?? '';
            if (in_array($eligibility, ['remotelev_phev', 'remotelev_bev', 'bev', 'phev'])) {
                $eligibility = self::ELIGIBILITY_REMOTELEV;
            } elseif ($eligibility === 'navco') {
                $eligibility = self::ELIGIBILITY_NAC;
            }

            if ($eligibility && !in_array($eligibility, $eligibilities)) {
                $eligibilities[] = $eligibility;
            }
        }
        return $eligibilities;
    }

    public function getVehicleType(string $vehicleType): int
    {
        return $this->vehicleFeatureService->getVehicleType($vehicleType);
    }

    public function removeNullValues(array $array): array
    {
        return $this->vehicleUtilityService->removeNullValues($array);
    }

    /**
     * {
     *   "success": {
     *     "vehicleInfo": {
     *       "vin": "VF30U9HD8ES290122",
     *       "lcdv": "1PTBSYHBM604A0F1M09P05FX",
     *       "visual": "https://visuel3d-secure.peugeot.com/V3DImage.ashx?client=MyMarque&format=png&color=0MM00N9P&trim=0P050RFX&back=0&width=1000&version=1PTBSYHBM604A0F1&view=001&OPT1=VD09&OPT2=WLWF&OPT3=ZD09&OPT4=ZH47",
     *       "short_label": "3008",
     *       "warranty_start_date": 1423612800,
     *       "attributes": [
     *         "DCD06CD",
     *         "DCW79CD",
     *         "DCX01CD",
     *         "DJY05CD",
     *         "DLX15CD",
     *         "DRCT6CD",
     *         "DRE07CD",
     *         "DVQ21CD",
     *         "DXD00CD"
     *       ],
     *       "type_vehicle": 0,
     *       "mileage": {
     *         "value": 0
     *       }
     *     },
     *     "eligibility": [
     *       "device_smeg",
     *       "smartappsv1"
     *     ],
     *     "vehicleProducts": {
     *       "productsCatalog": [],
     *       "purchasedProducts": [],
     *       "productGroupNameStatus": {}
     *     },
     *     "settingsUpdate": 1736851272
     *   }
     * }
     */
    public function mapVehicleInfoXPFormat(array $vehicle, array $catalogResponse, array $subscriptionResponse, array $productsStatus, array $featureCodes): array
    {
        $type = $this->getVehicleType($vehicle['type'] ?? '');
        $vehicleXPFormat = [
            'vehicleInfo' => [
                'vin' => $vehicle['vin'] ?? '',
                'lcdv' => $vehicle['lcdv'] ?? '',
                'visual' => $vehicle['picture'] ?? '',
                'short_label' => $vehicle['nickName'] ?? $vehicle['shortLabel'],
                'nickname' => $vehicle['nickName'] ?? '',
                'warranty_start_date' => $vehicle['warrantyStartDate'] ?? null,
                'attributes' => $vehicle['attributes'] ?? [],
                'type_vehicle' => $type,
                'mileage' => $vehicle['mileage'] ?? new stdClass(),
                'versionId' => $vehicle['versionId'] ?? '',
                'brand' => $vehicle['make'] ?? '',
                'subMake' => $vehicle['subMake'] ?? '',
                'country' => $vehicle['market'] ?? '',
                'regTimeStamp' => $vehicle['regTimeStamp'] ?? '',
                'year' => $vehicle['year'] ?? '',
                'sdp' => $vehicle['sdp'] ?? '',
                'isOrder' => $vehicle['isOrder'] ?? '',
                'features' => $featureCodes ?? new stdClass(),
                'addStatus' => $vehicle['addStatus'] ?? '',
                'isO2x' => $vehicle['isO2x'] ?? '',
                'criteriaValue' => $vehicle['criteriaValue'] ?? null
            ],
            'eligibility' => $this->setEligibilityFromContracts($subscriptionResponse),
            'vehicleProducts' => [
                'productsCatalog' => $catalogResponse,
                'purchasedProducts' => $subscriptionResponse,
                'productGroupNameStatus' => empty($productsStatus) ? new stdClass() : $productsStatus
            ],
            'settingsUpdate' => $vehicle['lastUpdate'] ?? null
        ];

        $vehicleXPFormat = $this->removeNullValues($vehicleXPFormat);
        return $vehicleXPFormat;
    }

    public function getVehicleDetail(
        string $userId,
        string $critariaValue,
        string $language,
        string $country,
        string $critariaKey = 'vin',
        string $source = 'APP'
    ) {
        try {
            $vehicleResponse = $this->vehicleUtilityService->getVehicle($userId, $critariaValue, $critariaKey);

            if (!$vehicleResponse) {
                $this->logger->error(__METHOD__ . ' : Error vehicle not exist');
                return new ErrorResponse('Vehicle not exist', Response::HTTP_NOT_FOUND);
            }

            $vehicleObject = $vehicleResponse['vehicle'][0] ?? null;
            if (!$vehicleObject) {
                $this->logger->error(__METHOD__ . ' : Error vehicle object not found');
                return new ErrorResponse('Vehicle object not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle object to array for backward compatibility
            $vehicle = [
                'vin' => $vehicleObject->getVin(),
                'brand' => $vehicleObject->getBrand(),
                'sdp' => $vehicleObject->getStatus(), // ODM uses status field instead of sdp
                'model' => $vehicleObject->getModel(),
                'versionId' => $vehicleObject->getVersionId(),
                'criteriaValue' => $critariaValue, // Add the criteria value used to find the vehicle
            ];

            $vin = $vehicle['vin'] ?? '';
            $brand = $vehicle['brand'] ?? '';

            // Get userDbId from user data
            $userData = $this->userDataService->findUserById($userId);
            $userDbId = null;
            if ($userData) {
                $userDbId = $userData->getUserDbId();
                $this->logger->info(__METHOD__ . ' : Retrieved userDbId from user data', [
                    'userId' => $userId,
                    'userDbId' => $userDbId
                ]);

                // If userDbId is null or empty, use userId as fallback
                if (empty($userDbId)) {
                    $this->logger->warning(__METHOD__ . ' : userDbId is empty, using userId as fallback', [
                        'userId' => $userId
                    ]);
                    $userDbId = $userId;
                }
            } else {
                $this->logger->warning(__METHOD__ . ' : User data not found, using userId as fallback for userDbId', [
                    'userId' => $userId
                ]);
                $userDbId = $userId; // Fallback to userId if user data not found
            }

            if (!$userDbId) {
                $this->logger->error(__METHOD__ . ' : Error userDbId not exist');
                return new ErrorResponse('UserDbId not exist', Response::HTTP_NOT_FOUND);
            }

            // Validate brand before calling Corvet API
            if (empty($brand)) {
                $this->logger->error(__METHOD__ . ' : Error vehicle brand is empty', [
                    'vin' => $vin,
                    'userId' => $userId,
                    'vehicleData' => $vehicle
                ]);
                return new ErrorResponse('Vehicle brand is required for Corvet API call', Response::HTTP_BAD_REQUEST);
            }

            $corvetData = $this->vehicleCorvetService->getCorvetData($vin, $brand);
            $this->logger->info(__METHOD__ . ' => Response from corvet service', ['corvetData' => $corvetData]);

            if (array_key_exists('error', $corvetData)) {
                $errorMessage = is_array($corvetData['error']) ? json_encode($corvetData['error']) : (string)$corvetData['error'];
                $this->logger->error(__METHOD__ . ' : Error while calling corvet service', [
                    'error' => $corvetData['error'],
                    'errorMessage' => $errorMessage
                ]);
                return new ErrorResponse('Error while calling corvet service: ' . $errorMessage, Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            // Check if vehicle exists in Corvet
            $vehicleExists = $corvetData['VEHICULE']['@attributes']['Existe'] ?? 'N';
            if ($vehicleExists === 'N') {
                $this->logger->warning(__METHOD__ . ' : Vehicle does not exist in Corvet system', [
                    'vin' => $vin,
                    'brand' => $brand,
                    'corvetResponse' => $corvetData
                ]);
                return new ErrorResponse('Vehicle not found in Corvet system', Response::HTTP_NOT_FOUND);
            }

            $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : LCDV not found in corvet data', [
                    'vin' => $vin,
                    'brand' => $brand,
                    'corvetData' => $corvetData
                ]);
                return new ErrorResponse('Vehicle LCDV not found', Response::HTTP_NOT_FOUND);
            }

            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->vehicleFeatureService->getManagedAttributesByPrefix($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);

            $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
            $vehicleSettings = json_decode($response->getData(), true)['documents'][0] ?? [];
            $this->logger->info(__METHOD__ . " => Response from findVehicleLabelByLcdv", ['vehicleSettings' => $vehicleSettings]);
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $label = $vehicleSettings['label'] ?? '';
            $vehicle['label'] = $label;
            $vehicle['lcdv'] = $lcdv;
            $vehicle['isO2x'] = $isO2x;
            $vehicle['attributes'] = $corvertAttributs;
            $params = [
                'userId' => $userId,
                'vin' => $vin,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'source' => $source,
                'userDbId' => $userDbId
            ];
            $target = 'B2C';

            $catalogResponse = $this->catalogManager->getCatalog($params);
            if ($catalogResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error while calling catalog service');
                return $catalogResponse;
            }
            $this->logger->info(__METHOD__ . " => Response from catalog service", ['catalogResponse' => $catalogResponse->getData()]);
            $catalogResponse = $catalogResponse->getData() ?? [];
            $productsStatus = array_fill_keys(array_column($catalogResponse, 'id', 'id'), 'disabled');

            $subscriptionResponse = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);
            if ($subscriptionResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error while calling subscription service');
                return $subscriptionResponse;
            }
            $this->logger->info("".__METHOD__." => Response from subscription service --> " . ['subscriptionResponse' => $subscriptionResponse->getData()]);
            $subscriptionResponse = $subscriptionResponse->getData() ?? [];
            $productsStatus = array_merge($productsStatus, array_fill_keys(array_map('strtolower', array_column($subscriptionResponse, 'type', 'type')), 'enabled'));

            //Feature codes
            $featureCodes  = $this->getUpdatedFeatureCodes($vehicle, $userId, $vin, $lcdv, $vehicleTypeNumber, $corvertAttributs, $userDbId);

            $vehicleXPFormat = $this->mapVehicleInfoXPFormat($vehicle, $catalogResponse, $subscriptionResponse, $productsStatus, $featureCodes);
            return new SuccessResponse($vehicleXPFormat ?? [], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception VehicleManager::getVehicleDetail ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function updateVehicleData(
        AddVehicleInputDTO $vehicleDto,
        AddVehicleOutputDTO $vehicleOutputDto
    ): WSResponse {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle data using ODM', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
            ]);

            // Create ODM Vehicle document from the data with ALL fields from original MongoDB Atlas implementation
            $vehicle = $this->userDataService->convertLegacyVehicleToODM([
                'id' => RefreshVehicleHelper::generateUid(), // Generate unique ID like original
                'vin' => $vehicleDto->getVin(),
                'brand' => $vehicleDto->getBrand(),
                'shortLabel' => $vehicleOutputDto->getLabel(),
                'modelDescription' => $vehicleOutputDto->getLabel(),
                'label' => $vehicleOutputDto->getLabel(),
                'versionId' => $vehicleOutputDto->getLcdv(),
                'featureCode' => $vehicleOutputDto->getFeaturesCode(),
                'featureCodeExpiry' => $vehicleOutputDto->getFeatureCodeExpiry(), // Add missing field
                'sdp' => $vehicleOutputDto->getSdp(),
                'picture' => $vehicleOutputDto->getVisual(),
                'type' => $vehicleOutputDto->getType(),
                'make' => $vehicleOutputDto->getMake(),
                'market' => $vehicleOutputDto->getMarket(),
                'country' => $vehicleDto->getCountry(), // Add missing country field
                'isO2X' => $vehicleOutputDto->getIsO2X(),
                'isOrder' => $vehicleOutputDto->getIsOrder(), // Add missing isOrder field
                'addStatus' => $vehicleOutputDto->getAddStatus(), // Add missing addStatus field
                'lastUpdate' => $vehicleOutputDto->getLastUpdate(),
                'regTimeStamp' => $vehicleOutputDto->getRegTimestamp(), // Fix field name to match original
                'year' => $vehicleOutputDto->getYear(), // Add missing year field
                'warrantyStartDate' => $vehicleOutputDto->getWarrantyStartDate(),
                'plugType' => $vehicleOutputDto->getConnectorType(), // Add missing plugType field (using connectorType)
                'nickName' => $vehicleOutputDto->getNickName(), // Add missing nickName field
                'mileage' => [ // Add missing mileage structure like original
                    'value' => (int) $vehicleDto->getMileage(),
                    'date' => time()
                ],
            ]);

            // Add vehicle using VehicleDataService
            $success = $this->vehicleDataService->addVehicleToUser($vehicleDto->getUserId(), $vehicle);

            if ($success) {
                return new WSResponse(Response::HTTP_OK, 'Vehicle added successfully');
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Failed to add vehicle');
            }

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle data', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'exception' => $e->getMessage()
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    private function addVehicleInCustomerAt(
        string $vin,
        string $label,
        string $lcdv,
        string $ticket,
        string $siteCode,
        string $language
    ): ?array {

        $data = [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
        // insert into- customer@
        $response = $this->systemUserDataClient->addVehicle($ticket, $siteCode, $language, $data);

        return $response->getData()['success'] ?? null;
    }

    /**
     * @param string $psaId
     * @param string $siteCode
     * 
     * @return string|null
     */
    private function getTicket(
        string $psaId,
        string $siteCode
    ): ?string {
        // get ticket from user data
        $userdata = $this->systemUserDataClient->getV1CatTicket($psaId, $siteCode);
        return $userdata->getData()['success']['ticket'] ?? null;
    }

    /**
     * @param string $userId
     * @param string $brand
     *
     * @return string|null
     */
    private function getPsaId(
        string $userId,
        string $brand
    ): ?string {
        $psaId = $this->userDataService->getPsaIdByUserAndBrand($userId, $brand);
        return RefreshVehicleHelper::parsePsaId($psaId ?? '');
    }











    public static function getDataFromAttributes(array $attributes): ?array
    {
        $data = [];
        foreach ($attributes as $attribute) {
            if (preg_match('/^P(.{4})|^D(.{4})CP$/i', subject: $attribute)) {
                $data[] = substr($attribute, 1, 4);
            }
        }

        return $data;
    }





    private function updateVehicleDetailFromSdprData(
        AddVehicleOutputDTO $vehicle,
        string $userId,
        string $critariaValue,
        string $critariaKey
    ): WSResponse {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle detail from SDPR data using ODM', [
                'userId' => $userId,
                'critariaKey' => $critariaKey,
                'critariaValue' => $critariaValue,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return new WSResponse(Response::HTTP_NOT_FOUND, 'User not found');
            }

            // Find the vehicle by criteria
            $targetVehicle = null;
            foreach ($userData->getVehicles() as $v) {
                $value = match($critariaKey) {
                    'vin' => $v->getVin(),
                    'brand' => $v->getBrand(),
                    'model' => $v->getModel(),
                    'versionId' => $v->getVersionId(),
                    default => null
                };

                if ($value === $critariaValue) {
                    $targetVehicle = $v;
                    break;
                }
            }

            if (!$targetVehicle) {
                return new WSResponse(Response::HTTP_NOT_FOUND, 'Vehicle not found');
            }

            // Update vehicle properties
            // Note: ODM Vehicle document may not have all these fields, so we'll update what's available
            if (method_exists($targetVehicle, 'setYear') && $vehicle->getYear()) {
                $targetVehicle->setYear($vehicle->getYear());
            }
            if (method_exists($targetVehicle, 'setNickName') && $vehicle->getNickName()) {
                $targetVehicle->setNickName($vehicle->getNickName());
            }
            // regTimestamp might need to be stored in a different field or as metadata

            $this->userDataService->saveUserData($userData);

            return new WSResponse(Response::HTTP_OK, 'Vehicle updated successfully');

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle detail from SDPR data', [
                'userId' => $userId,
                'critariaKey' => $critariaKey,
                'critariaValue' => $critariaValue,
                'exception' => $e->getMessage()
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    private function updateVehicle(
        EditVehicleInputDTO $vehicleDto
    ): bool {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle using ODM', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'mileage' => $vehicleDto->getMileage(),
                'nickName' => $vehicleDto->getNickName(),
                'licencePlate' => $vehicleDto->getLicencePlate(),
            ]);

            $userData = $this->userDataService->findUserById($vehicleDto->getUserId());
            if (!$userData) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $vehicleDto->getUserId()
                ]);
                return false;
            }

            $vehicle = $userData->findVehicleByVin($vehicleDto->getVin());
            if (!$vehicle) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $vehicleDto->getUserId(),
                    'vin' => $vehicleDto->getVin()
                ]);
                return false;
            }

            // Update license plate (registration number)
            if ($vehicleDto->getLicencePlate()) {
                $vehicle->setRegistrationNumber($vehicleDto->getLicencePlate());
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated license plate', [
                    'vin' => $vehicleDto->getVin(),
                    'licencePlate' => $vehicleDto->getLicencePlate()
                ]);
            }

            // Update mileage using ODM MileageData object
            if ($vehicleDto->getMileage() !== null) {
                $mileageData = $vehicle->getMileage();
                if (!$mileageData) {
                    // Create new MileageData object if it doesn't exist
                    $mileageData = new MileageData();
                    $vehicle->setMileage($mileageData);
                }

                $mileageData->setValue((int) $vehicleDto->getMileage());

                // Set timestamp which will auto-set the date
                $timestamp = $vehicleDto->getMileageDate() ?? time();
                $mileageData->setTimestamp($timestamp);

                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated mileage', [
                    'vin' => $vehicleDto->getVin(),
                    'mileage' => $vehicleDto->getMileage(),
                    'mileageTimestamp' => $timestamp
                ]);
            }

            // Update nickName using ODM Vehicle object
            if ($vehicleDto->getNickName()) {
                $vehicle->setNickName($vehicleDto->getNickName());
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated nickName', [
                    'vin' => $vehicleDto->getVin(),
                    'nickName' => $vehicleDto->getNickName()
                ]);
            }

            $this->userDataService->saveUserData($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Successfully updated vehicle', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin()
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'exception' => $e->getMessage()
            ]);

            return false;
        }
    }


    /**
     * create or update vehicle data function.
     */
    public function deleteVehicle(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            // First check if user exists
            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $this->logger->error(__METHOD__ . ' User not found', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            $dbUserData = $this->userDataService->getVehicleAndUserDBIdByUserIdAndVin($userId, $vin);
            if ($dbUserData != null && isset($dbUserData)) {
                $deleteResponse = $this->userDataService->removeUserSSDPVehicles($userId, $vin);
                if ($deleteResponse) {
                    $this->logger->info(
                        'Deleted successfully.'
                    );
                    $userDBResponse = $this->systemUserDBService->deleteCustomerVehicle($dbUserData["userDbId"], $vin);
                    if (Response::HTTP_OK !== $userDBResponse->getCode() && Response::HTTP_NO_CONTENT !== $userDBResponse->getCode()) {
                        $this->logger->error(
                            'An error has occurred while deleting Vehicle from User in SSDP',
                            [
                                'userDbId' => $dbUserData["userDbId"],
                                'vin' => $vin
                            ]
                        );
                        $this->vehicleDataService->addVehicleToUser($userId, $dbUserData["vehicle"]);
                        return new ErrorResponse("Error while deleting the vin in SSDP. userId " . $userId . " vin " . $vin);
                    }
                    return new SuccessResponse("", Response::HTTP_NO_CONTENT);
                } else {
                    $this->logger->error(__METHOD__ . ' Failed to delete vehicle from user data', [
                        'userId' => $userId,
                        'vin' => $vin
                    ]);
                    return new ErrorResponse('Failed to delete vehicle from user data', Response::HTTP_INTERNAL_SERVER_ERROR);
                }
            } else {
                $this->logger->error(__METHOD__ . ' Vehicle not found for deletion', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get vehicle features
     * 
     * @param string $userId User ID
     * @param string $vin Vehicle Identification Number
     * 
     * @return ErrorResponse|SuccessResponse
     */
    public function getVehicleFeatures(string $userId, string $vin)
    {
        return $this->vehicleFeatureService->getVehicleFeatures($userId, $vin);
    }

    // START::STRICTLY FOR TESTING FEATURE CODES ONLY
    public function testFeaureCodes(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
            $corvetData = $this->mockCorvetData();
            $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->vehicleFeatureService->getManagedAttributesByPrefix($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';

            // Get userDbId from user data - properly extract it from ODM UserData document
            $userData = $this->userDataService->findUserById($vehicleDto->getUserId());
            $userDbId = null;
            if ($userData) {
                $userDbId = $userData->getUserDbId();
                $this->logger->info(__METHOD__ . ' : Retrieved userDbId from user data', [
                    'userId' => $vehicleDto->getUserId(),
                    'userDbId' => $userDbId
                ]);
            } else {
                $this->logger->warning(__METHOD__ . ' : User data not found, using userId as fallback for userDbId', [
                    'userId' => $vehicleDto->getUserId()
                ]);
                $userDbId = $vehicleDto->getUserId(); // Fallback to userId if user data not found
            }

            $featureCodes = $this->featureCodeService->getFeaturesCode( $vehicleDto->getUserId(), $vehicleDto->getVin(), $lcdv, $vehicleType, $corvertAttributs, null, null, true, $userDbId);
            return new SuccessResponse($featureCodes);
    }
    // END::STRICTLY FOR TESTING FEATURE CODES ONLY

    public function handleCustomerRightsUpdate(
        string $eventType, 
        string $vin, 
        string $userId, 
        string $timestamp,
        string $gigya_uid, 
        ?string $carAssociationLevel = null
    ): bool {
        try {
            $this->logger->info('Handling customer rights update', [
                'vin' => $vin,
                'userId' => $userId,
                'eventType' => $eventType,
                'timestamp' => $timestamp,
                'gigya_uid' => $gigya_uid, //userId in DB
                'carAssociationLevel' => $carAssociationLevel
            ]);
            
            // switch ($eventType) {
            //     case 'UPDATED_VEHICLE_CONTRACTS':
                    $userDocument = $this->userDataService->getVehicleAndUserIdByVin($gigya_uid, $vin);
                    if (!$userDocument) {
                        $this->logger->error(__CLASS__ . '::' . __METHOD__."No user data found for VIN: {$vin}", [
                            'vin' => $vin,
                            'gigya_uid' => $gigya_uid,
                            'userId' => $userId,
                            'eventType' => $eventType
                        ]);
                        return false;
                    }
                    $userId = $userDocument['userId'];
                    $vehicleObj = $userDocument['vehicle'];
                    $vehicleType = $vehicleObj->getType();
                    $lcdv = $vehicleObj->getVersionId();
                    $brand = $vehicleObj->getBrand();
                    $f2mcObj = $userDocument['f2mc'] ?? null;
                    $country = 'AT'; // Default country since ODM Vehicle doesn't store country
                    $userDbId = $userDocument['userDbId'] ?? null;
        
                    //Call corvet API
                    $corvetData = $this->vehicleCorvetService->getCorvetData($vin, $brand);
                    // $corvetData = $this->mockCorvetData(); //TODO remove this
                    $lcdv = $this->vehicleCorvetService->getLcdv($corvetData);
                    if (!$lcdv) {
                        $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                        return false;
                    }

                    $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
                    $corvertAttributs = $this->vehicleFeatureService->getManagedAttributesByPrefix($allAttributes);
        
                    // Regenerate feature codes
                    $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, $f2mcObj, $country, false, $userDbId);

                    // get existing NON FDS feature codes
                    $existingNonFDSFeatures = [];
                    $currentFeatureCodes = $vehicleObj->getFeatureCodes() ?? [];
                    $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
                    foreach ($currentFeatureCodes as $featureCode) {
                        if (in_array($featureCode['code'], $nonFdsKeys)) {
                            $existingNonFDSFeatures[] = $featureCode;
                        }
                    }
                   
                    $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
                    if (empty($featureCodes)) {
                        $this->logger->warning(__CLASS__ . '::' . __METHOD__."No feature codes generated for VIN: {$vin}, User ID: {$userId}");
                        return false;
                    }
            
                    if (!$this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes)) {
                        $this->logger->error(__CLASS__ . '::' . __METHOD__."Failed to update feature codes for VIN: {$vin}, User ID: {$userId}");
                        return false;
                    }
            
                    $this->logger->info(__CLASS__ . '::' . __METHOD__."Successfully updated feature codes for VIN: {$vin}, User ID: {$userId}");
                    // break;
            //     case 'CUSTOMER_RIGHTS_UPDATED':
            //         break;
            // }
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Error handling customer rights update', [
                'exception' => $e->getMessage(),
                'vin' => $vin,
                'userId' => $userId,
                'eventType' => $eventType
            ]);
            
            return false;
        }
    }

    /**
     * Gets the most recent feature codes for a vehicle, regenerating them if expired
     * 
     * @param array $vehicle The vehicle data array
     * @param string $userId The user ID
     * @param string $vin The vehicle identification number
     * @param string $lcdv The LCDV code
     * @param string $vehicleType The vehicle type
     * @param array $corvertAttributs The corvert attributes
     * @return array The feature codes array
     */
    private function getUpdatedFeatureCodes(
        array $vehicle,
        string $userId,
        string $vin,
        string $lcdv,
        string $vehicleType,
        array $corvertAttributs,
        string $userDbId
    ): array {
        $featureCodes = $vehicle['featureCode'] ?? [];
        $featureCodeExpiry = $vehicle['featureCodeExpiry'] ?? null;
        $currentTime = time();

        // Check if feature codes have expired
        if ($featureCodeExpiry === null || $currentTime > $featureCodeExpiry) {
            $this->logger->info(__METHOD__ . ' : Feature codes have expired or no expiry set, regenerating...', [
                'userId' => $userId,
                'vin' => $vin,
                'currentTime' => $currentTime,
                'featureCodeExpiry' => $featureCodeExpiry
            ]);
            
            // Regenerate feature codes
            $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);

            // get existing NON FDS feature codes
            $existingNonFDSFeatures = [];
            $currentFeatureCodes = $vehicle['featureCode'] ?? [];
            $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
            foreach ($currentFeatureCodes as $featureCode) {
                if (in_array($featureCode['code'], $nonFdsKeys)) {
                    $existingNonFDSFeatures[] = $featureCode;
                }
            }

            $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
            
            // Update the vehicle document with new feature codes and expiry
            $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
        }
        // updating each time since webhook is not triggered for feature codes
        else {
            $this->logger->info(__METHOD__ . ' : Feature codes have are regenerating...', [
                'userId' => $userId,
                'vin' => $vin,
                'currentTime' => $currentTime,
                'featureCodeExpiry' => $featureCodeExpiry
            ]);
            
            // Regenerate feature codes
            $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);

            // get existing NON FDS feature codes
            $existingNonFDSFeatures = [];
            $currentFeatureCodes = $vehicle['featureCode'] ?? [];
            $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
            foreach ($currentFeatureCodes as $featureCode) {
                if (in_array($featureCode['code'], $nonFdsKeys)) {
                    $existingNonFDSFeatures[] = $featureCode;
                }
            }

            $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
            
            // Update the vehicle document with new feature codes and expiry
            $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
        }
        return $featureCodes;
    }


}
